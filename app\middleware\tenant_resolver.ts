import Tenant from '#models/tenant_model' // 👈 Import Tenant model
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import db from '@adonisjs/lucid/services/db'

export default class TenantResolver {
  public async handle({ request }: HttpContext, next: NextFn) {
    // 👇 You can choose to resolve by subdomain or header
    const tenantSlug = request.header('x-tenant') 
      || request.subdomains()[0] // e.g., acme.my-ai-studio.com → "acme"

    if (!tenantSlug) {
      throw new Error('Tenant not specified')
    }

    // 🔎 Look up tenant in master DB
    const tenant = await Tenant.query({ connection: 'master' })
      .where('slug', tenantSlug)
      .firstOrFail()

    // ⚡️ Dynamically register tenant DB connection (overwrite per request)
    db.manager.add('tenant', {
      client: 'pg',
      connection: {
        host: tenant.dbHost,
        port: tenant.dbPort,
        user: tenant.dbUser,
        password: tenant.dbPassword,
        database: tenant.dbName,
      },
    })

    

    // 🟢 Continue request
    await next()
  }
}
